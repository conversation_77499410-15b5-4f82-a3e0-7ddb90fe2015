{"name": "carbook", "firstRun": false, "exportConfig": true, "fileConfigs": [], "fileTree": {"expandedDirs": [], "hideSystemFiles": true, "systemFiles": [".*", "desktop.ini", "prepros.config", "$RECYCLE.BIN", "prepros.cfg", "prepros-6.config", "Prepros Export"], "hideUnwatchedFiles": false}, "imports": [{"path": "scss/style.scss", "imports": ["scss/bootstrap/bootstrap.scss", "scss/bootstrap/_variables.scss", "scss/bootstrap/_functions.scss", "scss/bootstrap/_mixins.scss", "scss/bootstrap/_root.scss", "scss/bootstrap/_reboot.scss", "scss/bootstrap/_type.scss", "scss/bootstrap/_images.scss", "scss/bootstrap/_code.scss", "scss/bootstrap/_grid.scss", "scss/bootstrap/_tables.scss", "scss/bootstrap/_forms.scss", "scss/bootstrap/_buttons.scss", "scss/bootstrap/_transitions.scss", "scss/bootstrap/_dropdown.scss", "scss/bootstrap/_button-group.scss", "scss/bootstrap/_input-group.scss", "scss/bootstrap/_custom-forms.scss", "scss/bootstrap/_nav.scss", "scss/bootstrap/_navbar.scss", "scss/bootstrap/_card.scss", "scss/bootstrap/_breadcrumb.scss", "scss/bootstrap/_pagination.scss", "scss/bootstrap/_badge.scss", "scss/bootstrap/_jumbotron.scss", "scss/bootstrap/_alert.scss", "scss/bootstrap/_progress.scss", "scss/bootstrap/_media.scss", "scss/bootstrap/_list-group.scss", "scss/bootstrap/_close.scss", "scss/bootstrap/_toasts.scss", "scss/bootstrap/_modal.scss", "scss/bootstrap/_tooltip.scss", "scss/bootstrap/_popover.scss", "scss/bootstrap/_carousel.scss", "scss/bootstrap/_spinners.scss", "scss/bootstrap/_utilities.scss", "scss/bootstrap/_print.scss", "scss/bootstrap/vendor/_rfs.scss", "scss/bootstrap/mixins/_deprecate.scss", "scss/bootstrap/mixins/_breakpoints.scss", "scss/bootstrap/mixins/_hover.scss", "scss/bootstrap/mixins/_image.scss", "scss/bootstrap/mixins/_badge.scss", "scss/bootstrap/mixins/_resize.scss", "scss/bootstrap/mixins/_screen-reader.scss", "scss/bootstrap/mixins/_size.scss", "scss/bootstrap/mixins/_reset-text.scss", "scss/bootstrap/mixins/_text-emphasis.scss", "scss/bootstrap/mixins/_text-hide.scss", "scss/bootstrap/mixins/_text-truncate.scss", "scss/bootstrap/mixins/_visibility.scss", "scss/bootstrap/mixins/_alert.scss", "scss/bootstrap/mixins/_buttons.scss", "scss/bootstrap/mixins/_caret.scss", "scss/bootstrap/mixins/_pagination.scss", "scss/bootstrap/mixins/_lists.scss", "scss/bootstrap/mixins/_list-group.scss", "scss/bootstrap/mixins/_nav-divider.scss", "scss/bootstrap/mixins/_forms.scss", "scss/bootstrap/mixins/_table-row.scss", "scss/bootstrap/mixins/_background-variant.scss", "scss/bootstrap/mixins/_border-radius.scss", "scss/bootstrap/mixins/_box-shadow.scss", "scss/bootstrap/mixins/_gradients.scss", "scss/bootstrap/mixins/_transition.scss", "scss/bootstrap/mixins/_clearfix.scss", "scss/bootstrap/mixins/_grid-framework.scss", "scss/bootstrap/mixins/_grid.scss", "scss/bootstrap/mixins/_float.scss", "scss/bootstrap/utilities/_align.scss", "scss/bootstrap/utilities/_background.scss", "scss/bootstrap/utilities/_borders.scss", "scss/bootstrap/utilities/_clearfix.scss", "scss/bootstrap/utilities/_display.scss", "scss/bootstrap/utilities/_embed.scss", "scss/bootstrap/utilities/_flex.scss", "scss/bootstrap/utilities/_float.scss", "scss/bootstrap/utilities/_overflow.scss", "scss/bootstrap/utilities/_position.scss", "scss/bootstrap/utilities/_screenreaders.scss", "scss/bootstrap/utilities/_shadows.scss", "scss/bootstrap/utilities/_sizing.scss", "scss/bootstrap/utilities/_stretched-link.scss", "scss/bootstrap/utilities/_spacing.scss", "scss/bootstrap/utilities/_text.scss", "scss/bootstrap/utilities/_visibility.scss"]}, {"path": "scss/bootstrap/_mixins.scss", "imports": ["scss/bootstrap/vendor/_rfs.scss", "scss/bootstrap/mixins/_deprecate.scss", "scss/bootstrap/mixins/_breakpoints.scss", "scss/bootstrap/mixins/_hover.scss", "scss/bootstrap/mixins/_image.scss", "scss/bootstrap/mixins/_badge.scss", "scss/bootstrap/mixins/_resize.scss", "scss/bootstrap/mixins/_screen-reader.scss", "scss/bootstrap/mixins/_size.scss", "scss/bootstrap/mixins/_reset-text.scss", "scss/bootstrap/mixins/_text-emphasis.scss", "scss/bootstrap/mixins/_text-hide.scss", "scss/bootstrap/mixins/_text-truncate.scss", "scss/bootstrap/mixins/_visibility.scss", "scss/bootstrap/mixins/_alert.scss", "scss/bootstrap/mixins/_buttons.scss", "scss/bootstrap/mixins/_caret.scss", "scss/bootstrap/mixins/_pagination.scss", "scss/bootstrap/mixins/_lists.scss", "scss/bootstrap/mixins/_list-group.scss", "scss/bootstrap/mixins/_nav-divider.scss", "scss/bootstrap/mixins/_forms.scss", "scss/bootstrap/mixins/_table-row.scss", "scss/bootstrap/mixins/_background-variant.scss", "scss/bootstrap/mixins/_border-radius.scss", "scss/bootstrap/mixins/_box-shadow.scss", "scss/bootstrap/mixins/_gradients.scss", "scss/bootstrap/mixins/_transition.scss", "scss/bootstrap/mixins/_clearfix.scss", "scss/bootstrap/mixins/_grid-framework.scss", "scss/bootstrap/mixins/_grid.scss", "scss/bootstrap/mixins/_float.scss"]}, {"path": "scss/bootstrap/_utilities.scss", "imports": ["scss/bootstrap/utilities/_align.scss", "scss/bootstrap/utilities/_background.scss", "scss/bootstrap/utilities/_borders.scss", "scss/bootstrap/utilities/_clearfix.scss", "scss/bootstrap/utilities/_display.scss", "scss/bootstrap/utilities/_embed.scss", "scss/bootstrap/utilities/_flex.scss", "scss/bootstrap/utilities/_float.scss", "scss/bootstrap/utilities/_overflow.scss", "scss/bootstrap/utilities/_position.scss", "scss/bootstrap/utilities/_screenreaders.scss", "scss/bootstrap/utilities/_shadows.scss", "scss/bootstrap/utilities/_sizing.scss", "scss/bootstrap/utilities/_stretched-link.scss", "scss/bootstrap/utilities/_spacing.scss", "scss/bootstrap/utilities/_text.scss", "scss/bootstrap/utilities/_visibility.scss"]}, {"path": "scss/bootstrap/bootstrap-grid.scss", "imports": ["scss/bootstrap/_functions.scss", "scss/bootstrap/_variables.scss", "scss/bootstrap/mixins/_breakpoints.scss", "scss/bootstrap/mixins/_grid-framework.scss", "scss/bootstrap/mixins/_grid.scss", "scss/bootstrap/_grid.scss", "scss/bootstrap/utilities/_display.scss", "scss/bootstrap/utilities/_flex.scss", "scss/bootstrap/utilities/_spacing.scss"]}, {"path": "scss/bootstrap/bootstrap-reboot.scss", "imports": ["scss/bootstrap/_functions.scss", "scss/bootstrap/_variables.scss", "scss/bootstrap/_mixins.scss", "scss/bootstrap/_reboot.scss", "scss/bootstrap/vendor/_rfs.scss", "scss/bootstrap/mixins/_deprecate.scss", "scss/bootstrap/mixins/_breakpoints.scss", "scss/bootstrap/mixins/_hover.scss", "scss/bootstrap/mixins/_image.scss", "scss/bootstrap/mixins/_badge.scss", "scss/bootstrap/mixins/_resize.scss", "scss/bootstrap/mixins/_screen-reader.scss", "scss/bootstrap/mixins/_size.scss", "scss/bootstrap/mixins/_reset-text.scss", "scss/bootstrap/mixins/_text-emphasis.scss", "scss/bootstrap/mixins/_text-hide.scss", "scss/bootstrap/mixins/_text-truncate.scss", "scss/bootstrap/mixins/_visibility.scss", "scss/bootstrap/mixins/_alert.scss", "scss/bootstrap/mixins/_buttons.scss", "scss/bootstrap/mixins/_caret.scss", "scss/bootstrap/mixins/_pagination.scss", "scss/bootstrap/mixins/_lists.scss", "scss/bootstrap/mixins/_list-group.scss", "scss/bootstrap/mixins/_nav-divider.scss", "scss/bootstrap/mixins/_forms.scss", "scss/bootstrap/mixins/_table-row.scss", "scss/bootstrap/mixins/_background-variant.scss", "scss/bootstrap/mixins/_border-radius.scss", "scss/bootstrap/mixins/_box-shadow.scss", "scss/bootstrap/mixins/_gradients.scss", "scss/bootstrap/mixins/_transition.scss", "scss/bootstrap/mixins/_clearfix.scss", "scss/bootstrap/mixins/_grid-framework.scss", "scss/bootstrap/mixins/_grid.scss", "scss/bootstrap/mixins/_float.scss"]}, {"path": "scss/bootstrap/bootstrap.scss", "imports": ["scss/bootstrap/_functions.scss", "scss/bootstrap/_variables.scss", "scss/bootstrap/_mixins.scss", "scss/bootstrap/_root.scss", "scss/bootstrap/_reboot.scss", "scss/bootstrap/_type.scss", "scss/bootstrap/_images.scss", "scss/bootstrap/_code.scss", "scss/bootstrap/_grid.scss", "scss/bootstrap/_tables.scss", "scss/bootstrap/_forms.scss", "scss/bootstrap/_buttons.scss", "scss/bootstrap/_transitions.scss", "scss/bootstrap/_dropdown.scss", "scss/bootstrap/_button-group.scss", "scss/bootstrap/_input-group.scss", "scss/bootstrap/_custom-forms.scss", "scss/bootstrap/_nav.scss", "scss/bootstrap/_navbar.scss", "scss/bootstrap/_card.scss", "scss/bootstrap/_breadcrumb.scss", "scss/bootstrap/_pagination.scss", "scss/bootstrap/_badge.scss", "scss/bootstrap/_jumbotron.scss", "scss/bootstrap/_alert.scss", "scss/bootstrap/_progress.scss", "scss/bootstrap/_media.scss", "scss/bootstrap/_list-group.scss", "scss/bootstrap/_close.scss", "scss/bootstrap/_toasts.scss", "scss/bootstrap/_modal.scss", "scss/bootstrap/_tooltip.scss", "scss/bootstrap/_popover.scss", "scss/bootstrap/_carousel.scss", "scss/bootstrap/_spinners.scss", "scss/bootstrap/_utilities.scss", "scss/bootstrap/_print.scss", "scss/bootstrap/vendor/_rfs.scss", "scss/bootstrap/mixins/_deprecate.scss", "scss/bootstrap/mixins/_breakpoints.scss", "scss/bootstrap/mixins/_hover.scss", "scss/bootstrap/mixins/_image.scss", "scss/bootstrap/mixins/_badge.scss", "scss/bootstrap/mixins/_resize.scss", "scss/bootstrap/mixins/_screen-reader.scss", "scss/bootstrap/mixins/_size.scss", "scss/bootstrap/mixins/_reset-text.scss", "scss/bootstrap/mixins/_text-emphasis.scss", "scss/bootstrap/mixins/_text-hide.scss", "scss/bootstrap/mixins/_text-truncate.scss", "scss/bootstrap/mixins/_visibility.scss", "scss/bootstrap/mixins/_alert.scss", "scss/bootstrap/mixins/_buttons.scss", "scss/bootstrap/mixins/_caret.scss", "scss/bootstrap/mixins/_pagination.scss", "scss/bootstrap/mixins/_lists.scss", "scss/bootstrap/mixins/_list-group.scss", "scss/bootstrap/mixins/_nav-divider.scss", "scss/bootstrap/mixins/_forms.scss", "scss/bootstrap/mixins/_table-row.scss", "scss/bootstrap/mixins/_background-variant.scss", "scss/bootstrap/mixins/_border-radius.scss", "scss/bootstrap/mixins/_box-shadow.scss", "scss/bootstrap/mixins/_gradients.scss", "scss/bootstrap/mixins/_transition.scss", "scss/bootstrap/mixins/_clearfix.scss", "scss/bootstrap/mixins/_grid-framework.scss", "scss/bootstrap/mixins/_grid.scss", "scss/bootstrap/mixins/_float.scss", "scss/bootstrap/utilities/_align.scss", "scss/bootstrap/utilities/_background.scss", "scss/bootstrap/utilities/_borders.scss", "scss/bootstrap/utilities/_clearfix.scss", "scss/bootstrap/utilities/_display.scss", "scss/bootstrap/utilities/_embed.scss", "scss/bootstrap/utilities/_flex.scss", "scss/bootstrap/utilities/_float.scss", "scss/bootstrap/utilities/_overflow.scss", "scss/bootstrap/utilities/_position.scss", "scss/bootstrap/utilities/_screenreaders.scss", "scss/bootstrap/utilities/_shadows.scss", "scss/bootstrap/utilities/_sizing.scss", "scss/bootstrap/utilities/_stretched-link.scss", "scss/bootstrap/utilities/_spacing.scss", "scss/bootstrap/utilities/_text.scss", "scss/bootstrap/utilities/_visibility.scss"]}], "projectView": {"selectedView": "file-tree"}, "fileWatcher": {"enabled": true, "watchedExtensions": ["less", "sass", "scss", "styl", "md", "markdown", "coffee", "js", "jade", "haml", "slim", "ls", "kit", "png", "jpg", "jpeg", "ts", "pug", "css", "html", "htm", "php"]}, "pathFilters": ["node_modules", ".*", "bower_components", "prepros.config", "Prepros Export", "prepros-6.config", "prepros.cfg", "wp-admin", "wp-includes"], "server": {"port": 8041, "assignNewPortAutomatically": true, "enable": true, "proxy": {"enable": false, "url": ""}}, "browser-sync": {"enable": false, "clicks": true, "forms": true, "scroll": true}, "live-reload": {"enable": true, "animate": true, "delay": 0}, "ftp-deploy": {"connectionType": "ftp", "remotePath": "", "uploadTimeout": 20000, "uploadOnChange": false, "ftp": {"secure": false, "keepAlive": true, "host": "", "port": 21, "user": "", "password": ""}, "sftp": {"host": "", "port": 22, "usePrivateKey": false, "username": "", "password": "", "privateKey": "", "passphrase": ""}, "pathFilters": ["config.rb", "prepros.config", "prepros-6.config", "node_modules", "Prepros Export", ".git", ".idea", ".sass-cache", ".hg", ".svn", ".cache", ".DS_Store", "*.sass", "*.scss", "*.less", "*.pug", "*.jade", "*.styl", "*.haml", "*.slim", "*.coffee", "*.ls", "*.kit", "*.ts"], "history": []}, "file-type-sass": "{\"compilers\":[\"node-sass\",\"autoprefixer\",\"minify-css\"]}", "file-type-less": "{\"compilers\":[\"less\",\"autoprefixer\",\"minify-css\"]}", "autoprefixer": {"browsers": "last 5 versions"}, "file-type-pug": "{\"compilers\":[\"pug\"]}", "file-type-css": "{\"compilers\":[\"autoprefixer\",\"cssnext\",\"minify-css\"]}", "file-type-javascript": "{\"compilers\":[\"concat-js\",\"babel\",\"uglify-js\"]}", "file-type-stylus": "{\"compilers\":[\"stylus\",\"autoprefixer\",\"minify-css\"]}", "file-type-markdown": "{\"compilers\":[\"markdown\"]}", "file-type-haml": "{\"compilers\":[\"haml\"]}", "file-type-slim": "{\"compilers\":[\"slim\"]}", "file-type-coffee-script": "{\"compilers\":[\"coffee-script\",\"uglify-js\"]}", "file-type-livescript": "{\"compilers\":[\"livescript\",\"uglify-js\"]}", "file-type-kit": "{\"compilers\":[\"kit\"]}", "uglify-js": {"ie8": false, "compress": {"sequences": true, "properties": true, "dead_code": true, "drop_debugger": true, "unsafe": false, "unsafe_comps": false, "unsafe_math": false, "unsafe_proto": false, "unsafe_regexp": false, "conditionals": true, "comparisons": true, "evaluate": true, "booleans": true, "loops": true, "unused": true, "toplevel": false, "top_retain": "", "hoist_funs": true, "hoist_vars": false, "if_return": true, "join_vars": true, "collapse_vars": true, "reduce_vars": true, "warnings": true, "negate_iife": true, "pure_getters": false, "pure_funcs": [], "drop_console": false, "expression": false, "keep_fargs": true, "keep_fnames": false, "passes": 1, "keep_infinity": false, "side_effects": true, "global_defs": []}, "output": {"ascii_only": false, "beautify": false, "comments": "", "indent_level": 4, "indent_start": 0, "inline_script": false, "keep_quoted_props": false, "max_line_len": false, "preamble": "", "preserve_line": false, "quote_keys": false, "quote_style": 0, "semicolons": true, "shebang": true, "width": 80}}, "cssnext": {"customProperties": true, "applyRule": true, "calc": false, "nesting": true, "customMedia": true, "mediaQueriesRange": true, "customSelectors": true, "attributeCaseInsensitive": true, "colorRebeccapurple": true, "colorHwb": true, "colorGray": true, "colorHexAlpha": true, "colorFunction": true, "fontVariant": true, "filter": true, "initial": true, "rem": true, "pseudoElements": true, "pseudoClassMatches": true, "pseudoClassNot": true, "pseudoClassAnyLink": true, "colorRgba": true, "overflowWrap": true}, "file-type-typescript": "{\"compilers\":[\"typescript\",\"uglify-js\"]}", "babel": {"useBabelRc": true, "presets": {"babel-preset-es2015": true}, "plugins": {"babel-plugin-syntax-jsx": true, "babel-plugin-transform-react-jsx": true, "babel-plugin-transform-async-to-generator": true, "babel-plugin-transform-class-properties": true, "babel-plugin-transform-object-rest-spread": true}}, "file-type-png": "{\"compilers\":[\"png\"]}", "file-type-jpg": "{\"compilers\":[\"jpg\"]}"}