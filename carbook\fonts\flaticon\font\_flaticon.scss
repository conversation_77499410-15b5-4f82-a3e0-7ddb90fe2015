    /*
    Flaticon icon font: Flaticon
    Creation date: 29/10/2019 06:11
    */

    @font-face {
  font-family: "Flaticon";
  src: url("./Flaticon.eot");
  src: url("./Flaticon.eot?#iefix") format("embedded-opentype"),
       url("./Flaticon.woff2") format("woff2"),
       url("./Flaticon.woff") format("woff"),
       url("./Flaticon.ttf") format("truetype"),
       url("./Flaticon.svg#Flaticon") format("svg");
  font-weight: normal;
  font-style: normal;
}

@media screen and (-webkit-min-device-pixel-ratio:0) {
  @font-face {
    font-family: "Flaticon";
    src: url("./Flaticon.svg#Flaticon") format("svg");
  }
}

    .fi:before{
        display: inline-block;
  font-family: "Flaticon";
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  line-height: 1;
  text-decoration: inherit;
  text-rendering: optimizeLegibility;
  text-transform: none;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  font-smoothing: antialiased;
    }

    .flaticon-wedding-car:before { content: "\f100"; }
.flaticon-car:before { content: "\f101"; }
.flaticon-suv:before { content: "\f102"; }
.flaticon-transportation:before { content: "\f103"; }
.flaticon-route:before { content: "\f104"; }
.flaticon-handshake:before { content: "\f105"; }
.flaticon-rent:before { content: "\f106"; }
.flaticon-dashboard:before { content: "\f107"; }
.flaticon-pistons:before { content: "\f108"; }
.flaticon-car-seat:before { content: "\f109"; }
.flaticon-backpack:before { content: "\f10a"; }
.flaticon-diesel:before { content: "\f10b"; }
    
    $font-Flaticon-wedding-car: "\f100";
    $font-Flaticon-car: "\f101";
    $font-Flaticon-suv: "\f102";
    $font-Flaticon-transportation: "\f103";
    $font-Flaticon-route: "\f104";
    $font-Flaticon-handshake: "\f105";
    $font-Flaticon-rent: "\f106";
    $font-Flaticon-dashboard: "\f107";
    $font-Flaticon-pistons: "\f108";
    $font-Flaticon-car-seat: "\f109";
    $font-Flaticon-backpack: "\f10a";
    $font-Flaticon-diesel: "\f10b";